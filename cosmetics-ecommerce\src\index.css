@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-white font-sans text-gray-900;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .btn-primary {
    @apply rounded-lg bg-primary-600 px-4 py-2 font-medium text-white transition-colors duration-200 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply rounded-lg bg-secondary-100 px-4 py-2 font-medium text-secondary-900 transition-colors duration-200 hover:bg-secondary-200 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
  }

  .btn-outline {
    @apply rounded-lg border border-primary-600 px-4 py-2 font-medium text-primary-600 transition-colors duration-200 hover:bg-primary-600 hover:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .card {
    @apply overflow-hidden rounded-xl border border-gray-100 bg-white shadow-soft;
  }

  .input-field {
    @apply w-full rounded-lg border border-gray-300 px-3 py-2 transition-colors duration-200 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary-500;
  }

  .skeleton {
    @apply animate-pulse rounded bg-gray-200;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
