import { useState, useTransition } from 'react';

function App() {
  const [count, setCount] = useState(0);
  const [isPending, startTransition] = useTransition();

  const handleIncrement = () => {
    startTransition(() => {
      setCount(count => count + 1);
    });
  };

  return (
    <div className='min-h-screen bg-gradient-to-br from-primary-50 to-accent-50'>
      <div className='container mx-auto px-4 py-16'>
        <div className='text-center'>
          <h1 className='mb-4 font-serif text-4xl font-bold text-primary-900 md:text-6xl'>
            Cosmetics E-Commerce
          </h1>
          <p className='mx-auto mb-8 max-w-2xl text-lg text-secondary-600'>
            Built with React 19, TypeScript, and Tailwind CSS
          </p>

          <div className='card mx-auto max-w-md p-8'>
            <h2 className='mb-4 text-2xl font-semibold text-gray-900'>
              React 19 Features Test
            </h2>

            <div className='space-y-4'>
              <button
                onClick={handleIncrement}
                disabled={isPending}
                className='btn-primary w-full disabled:opacity-50'
              >
                {isPending ? 'Updating...' : `Count: ${count}`}
              </button>

              <div className='text-sm text-gray-600'>
                <p>✅ React 19 useTransition</p>
                <p>✅ TypeScript strict mode</p>
                <p>✅ Tailwind CSS v3</p>
                <p>✅ Vite build system</p>
              </div>
            </div>
          </div>

          <div className='mx-auto mt-12 grid max-w-4xl grid-cols-1 gap-6 md:grid-cols-3'>
            <div className='card p-6 text-center'>
              <div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary-100'>
                <span className='text-xl text-primary-600'>🛍️</span>
              </div>
              <h3 className='mb-2 font-semibold text-gray-900'>
                Product Catalog
              </h3>
              <p className='text-sm text-gray-600'>
                Advanced filtering and search
              </p>
            </div>

            <div className='card p-6 text-center'>
              <div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-accent-100'>
                <span className='text-xl text-accent-600'>🛒</span>
              </div>
              <h3 className='mb-2 font-semibold text-gray-900'>
                Shopping Cart
              </h3>
              <p className='text-sm text-gray-600'>
                Persistent cart with optimistic updates
              </p>
            </div>

            <div className='card p-6 text-center'>
              <div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-secondary-100'>
                <span className='text-xl text-secondary-600'>👤</span>
              </div>
              <h3 className='mb-2 font-semibold text-gray-900'>User Account</h3>
              <p className='text-sm text-gray-600'>
                Authentication and profile management
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
