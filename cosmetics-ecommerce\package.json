{"name": "cosmetics-ecommerce", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.7", "@hookform/resolvers": "^5.2.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.84.1", "autoprefixer": "^10.4.21", "currency.js": "^2.0.4", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.6.1", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.7.1", "tailwindcss": "^3.4.17", "zod": "^4.0.14", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^24.1.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}