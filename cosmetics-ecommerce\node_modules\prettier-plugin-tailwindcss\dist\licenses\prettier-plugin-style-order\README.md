# Style Order for Prettier

__Style Order__ is a [Prettier](https://prettier.io/) plugin which sorts property declarations and groups related properties.

## Installation

```bash
npm i -D prettier-plugin-style-order
```

That's it! Prettier should automatically find/use the plugin.

If it doesn't seem to be working, refer to Pretti<PERSON>'s [plugin docs](https://prettier.io/docs/en/plugins.html) for more in-depth directions.

## Credits

- [`postcss-sorting`](https://github.com/hudochenkov/postcss-sorting)
- [`stylelint-config-rational-order`](https://github.com/constverum/stylelint-config-rational-order)
- [`prettier-plugin-package`](https://github.com/shellscape/prettier-plugin-package)