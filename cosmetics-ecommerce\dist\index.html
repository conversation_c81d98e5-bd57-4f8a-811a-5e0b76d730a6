<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Cosmetics E-Commerce | React 19 + TypeScript</title>
    <script type="module" crossorigin src="/assets/index--7_zgFxI.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-CiW5Bwbg.js">
    <link rel="modulepreload" crossorigin href="/assets/router-BI41mnUw.js">
    <link rel="modulepreload" crossorigin href="/assets/ui-DJo4UDBK.js">
    <link rel="stylesheet" crossorigin href="/assets/index-DAsTnhku.css">
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
